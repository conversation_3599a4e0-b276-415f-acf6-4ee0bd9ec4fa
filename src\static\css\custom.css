/* Custom CSS for Movie Search Application */

/* Additional animations and effects */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

/* Loading spinner */
.loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Enhanced movie cards */
.movie-card-enhanced {
    transition: all 0.3s ease;
    border: none;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.movie-card-enhanced:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
}

/* Search input enhancements */
.search-input-container {
    position: relative;
}

.search-input-container .form-control {
    padding-left: 50px;
}

.search-input-container .search-icon {
    position: absolute;
    left: 15px;
    top: 50%;
    transform: translateY(-50%);
    color: #6c757d;
    z-index: 10;
}

/* Dropdown Search Component */
.dropdown-search-container {
    position: relative;
    width: 100%;
}

.dropdown-search-input {
    width: 100%;
    padding: 12px 50px 12px 20px;
    border: 2px solid rgba(255, 255, 255, 0.2);
    border-radius: 25px;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    color: white;
    font-size: 16px;
    transition: all 0.3s ease;
}

.dropdown-search-input:focus {
    outline: none;
    border-color: rgba(255, 255, 255, 0.5);
    background: rgba(255, 255, 255, 0.15);
    box-shadow: 0 0 20px rgba(255, 255, 255, 0.1);
}

.dropdown-search-input::placeholder {
    color: rgba(255, 255, 255, 0.7);
}

.dropdown-search-icon {
    position: absolute;
    right: 20px;
    top: 50%;
    transform: translateY(-50%);
    color: rgba(255, 255, 255, 0.7);
    font-size: 18px;
    pointer-events: none;
    transition: all 0.3s ease;
}

.dropdown-search-container.loading .dropdown-search-icon {
    animation: spin 1s linear infinite;
}

.dropdown-search-results {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(15px);
    border-radius: 15px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2);
    max-height: 400px;
    overflow-y: auto;
    z-index: 1000;
    margin-top: 8px;
    opacity: 0;
    transform: translateY(-10px);
    visibility: hidden;
    transition: all 0.3s ease;
}

.dropdown-search-results.show {
    opacity: 1;
    transform: translateY(0);
    visibility: visible;
}

.dropdown-search-item {
    display: flex;
    align-items: center;
    padding: 12px 16px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    cursor: pointer;
    transition: all 0.2s ease;
    text-decoration: none;
    color: inherit;
}

.dropdown-search-item:hover,
.dropdown-search-item.selected {
    background: rgba(102, 126, 234, 0.1);
    transform: translateX(5px);
    text-decoration: none;
    color: inherit;
}

.dropdown-search-item:last-child {
    border-bottom: none;
    border-radius: 0 0 15px 15px;
}

.dropdown-search-item:first-child {
    border-radius: 15px 15px 0 0;
}

.dropdown-search-poster {
    width: 50px;
    height: 75px;
    object-fit: cover;
    border-radius: 8px;
    margin-right: 15px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
    transition: transform 0.2s ease;
}

.dropdown-search-item:hover .dropdown-search-poster {
    transform: scale(1.05);
}

.dropdown-search-poster-placeholder {
    width: 50px;
    height: 75px;
    background: linear-gradient(135deg, #667eea, #764ba2);
    border-radius: 8px;
    margin-right: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 12px;
    text-align: center;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.dropdown-search-info {
    flex: 1;
    min-width: 0;
}

.dropdown-search-title {
    font-weight: 600;
    font-size: 16px;
    color: #333;
    margin: 0 0 4px 0;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.dropdown-search-year {
    font-size: 14px;
    color: #666;
    margin: 0;
}

.dropdown-search-no-results {
    padding: 20px;
    text-align: center;
    color: #666;
    font-style: italic;
}

.dropdown-search-loading {
    padding: 20px;
    text-align: center;
    color: #666;
}

.dropdown-search-loading .loading-spinner {
    margin-right: 10px;
}

/* Rating stars */
.rating-stars {
    color: #ffc107;
    font-size: 1.2rem;
}

/* Genre tags */
.genre-tag {
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
    margin: 2px;
    display: inline-block;
    transition: transform 0.2s ease;
}

.genre-tag:hover {
    transform: scale(1.1);
}

/* Poster image enhancements */
.poster-container {
    position: relative;
    overflow: hidden;
    border-radius: 15px;
}

.poster-container img {
    transition: transform 0.3s ease;
}

.poster-container:hover img {
    transform: scale(1.1);
}

/* Success and error messages */
.alert-custom {
    border: none;
    border-radius: 15px;
    padding: 1rem 1.5rem;
    margin: 1rem 0;
}

.alert-success-custom {
    background: linear-gradient(45deg, #28a745, #20c997);
    color: white;
}

.alert-error-custom {
    background: linear-gradient(45deg, #dc3545, #fd7e14);
    color: white;
}

/* Navigation enhancements */
.navbar-custom {
    backdrop-filter: blur(20px);
    background: rgba(255, 255, 255, 0.1) !important;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

/* Footer styling */
.footer-custom {
    background: rgba(0, 0, 0, 0.2);
    backdrop-filter: blur(10px);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    margin-top: 4rem;
    padding: 2rem 0;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .movie-card-enhanced {
        margin-bottom: 1.5rem;
    }

    .search-input-container .form-control {
        padding-left: 15px;
        font-size: 16px; /* Prevent zoom on iOS */
    }

    .search-input-container .search-icon {
        display: none;
    }

    /* Dropdown search mobile adjustments */
    .dropdown-search-input {
        padding: 14px 50px 14px 20px;
        font-size: 16px; /* Prevent zoom on iOS */
    }

    .dropdown-search-results {
        max-height: 300px;
        margin-top: 5px;
    }

    .dropdown-search-item {
        padding: 10px 12px;
    }

    .dropdown-search-poster,
    .dropdown-search-poster-placeholder {
        width: 40px;
        height: 60px;
        margin-right: 12px;
    }

    .dropdown-search-title {
        font-size: 14px;
    }

    .dropdown-search-year {
        font-size: 12px;
    }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    .card {
        background: rgba(255, 255, 255, 0.1) !important;
        color: white;
    }
    
    .text-muted {
        color: rgba(255, 255, 255, 0.7) !important;
    }
}

/* Print styles */
@media print {
    .navbar, .footer-custom, .btn {
        display: none !important;
    }
    
    body {
        background: white !important;
        color: black !important;
    }
    
    .card {
        background: white !important;
        color: black !important;
        box-shadow: none !important;
        border: 1px solid #ddd !important;
    }
}
