# Environment Variables for Movie Search Application
# Copy this file to .env and fill in your actual values

# OMDb API Configuration
API_OMDB_API_KEY=your-omdb-api-key-here
API_OMDB_BASE_URL=http://www.omdbapi.com/
API_TIMEOUT=10
API_MAX_RETRIES=3

# Flask Application Configuration
APP_NAME=Movie Search App
APP_VERSION=1.0.0
APP_DEBUG=false
APP_HOST=127.0.0.1
APP_PORT=5000
APP_SECRET_KEY=your-secret-key-change-this-in-production

# Feature Flags
FEATURES_ENABLE_POPULAR_MOVIES=true
FEATURES_ENABLE_API_ENDPOINTS=true
FEATURES_ENABLE_SHARING=true
FEATURES_CACHE_DURATION=3600

# UI Configuration
UI_MOVIES_PER_PAGE=12
UI_POPULAR_MOVIES_COUNT=10

# Security Settings (for production)
# SSL_DISABLE=false
# FORCE_HTTPS=true
# SESSION_COOKIE_SECURE=true
# SESSION_COOKIE_HTTPONLY=true
