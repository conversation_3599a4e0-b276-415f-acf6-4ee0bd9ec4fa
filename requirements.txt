# Movie Search Application Dependencies

# Core Flask Framework
Flask==3.1.1
Werkzeug==3.1.3

# HTTP Requests
requests==2.32.4

# Template Engine (included with Flask)
Jinja2==3.1.6

# Security and Utilities
MarkupSafe==3.0.2
itsdangerous==2.2.0
click==8.2.1
blinker==1.9.0

# Optional: Environment Variable Management
python-dotenv==1.0.1

# Optional: Production WSGI Server
gunicorn==23.0.0

# Optional: Development and Testing
pytest==8.3.4
pytest-flask==1.3.0
coverage==7.6.9

# Optional: Code Quality
flake8==7.1.1
black==24.10.0

# Optional: Type Checking
mypy==1.13.0

# Optional: Security Scanning
safety==3.2.11

# Optional: Performance Monitoring
flask-talisman==1.1.0

# Machine Learning Dependencies for Recommendation System
pandas==2.2.3
numpy==2.1.3
scikit-learn==1.5.2
nltk==3.9.1
joblib==1.4.2

# Optional: Additional ML utilities
scipy==1.14.1
matplotlib==3.9.2
seaborn==0.13.2
