---
name: Bug report
about: Create a report to help us improve the AI/ML recommendation system
title: '[BUG] '
labels: 'bug'
assignees: ''

---

**Describe the bug**
A clear and concise description of what the bug is.

**To Reproduce**
Steps to reproduce the behavior:
1. Go to '...'
2. Click on '....'
3. <PERSON>roll down to '....'
4. See error

**Expected behavior**
A clear and concise description of what you expected to happen.

**Screenshots**
If applicable, add screenshots to help explain your problem.

**Environment (please complete the following information):**
 - OS: [e.g. Windows, macOS, Linux]
 - Python Version: [e.g. 3.9.0]
 - Browser [e.g. chrome, safari]
 - Version [e.g. 22]

**ML Model Information:**
 - Similarity matrix loaded: [Yes/No]
 - Number of movies in dataset: [e.g. 5000]
 - Preprocessing completed: [Yes/No]

**Additional context**
Add any other context about the problem here.
