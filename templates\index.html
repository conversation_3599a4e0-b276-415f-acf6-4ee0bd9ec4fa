{% extends "base.html" %}

{% block title %}Movie Search - Find Your Favorite Movies{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-8">
        <div class="text-center mb-4">
            <h1 class="text-white mb-3">
                <i class="fas fa-film me-3"></i>Discover Moviess
            </h1>
            <p class="text-white-50 lead">Search for any movie and get detailed information instantly</p>
        </div>

        <div class="search-form">
            <div class="row g-3 align-items-center">
                <div class="col-md-9">
                    {% set search_placeholder = "Enter movie title (e.g., Inception, The Matrix, Titanic...)" %}
                    {% include 'components/search_dropdown.html' with context %}
                </div>
                <div class="col-md-3">
                    <div class="d-grid">
                        <button type="button" class="btn btn-primary btn-lg" onclick="performTraditionalSearch()">
                            <i class="fas fa-search me-2"></i>Search
                        </button>
                    </div>
                </div>
            </div>

            <!-- Fallback form for traditional search -->
            <form id="fallback-search-form" method="POST" action="{{ url_for('search_movie') }}" style="display: none;">
                <input type="hidden" name="movie_title" id="fallback-movie-title">
            </form>
        </div>

        {% if error %}
        <div class="alert alert-danger" role="alert">
            <i class="fas fa-exclamation-triangle me-2"></i>{{ error }}
        </div>
        {% endif %}

        <div class="card">
            <div class="card-body">
                <h5 class="card-title">
                    <i class="fas fa-lightbulb me-2 text-warning"></i>Try searching for:
                </h5>
                <div class="row">
                    <div class="col-md-6">
                        <ul class="list-unstyled">
                            <li><i class="fas fa-chevron-right me-2 text-primary"></i>Inception</li>
                            <li><i class="fas fa-chevron-right me-2 text-primary"></i>The Matrix</li>
                            <li><i class="fas fa-chevron-right me-2 text-primary"></i>Pulp Fiction</li>
                            <li><i class="fas fa-chevron-right me-2 text-primary"></i>The Godfather</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <ul class="list-unstyled">
                            <li><i class="fas fa-chevron-right me-2 text-primary"></i>Titanic</li>
                            <li><i class="fas fa-chevron-right me-2 text-primary"></i>Avatar</li>
                            <li><i class="fas fa-chevron-right me-2 text-primary"></i>Forrest Gump</li>
                            <li><i class="fas fa-chevron-right me-2 text-primary"></i>The Dark Knight</li>
                        </ul>
                    </div>
                </div>
                <div class="text-center mt-3">
                    <a href="{{ url_for('popular_movies') }}" class="btn btn-outline-primary">
                        <i class="fas fa-star me-2"></i>View Popular Movies
                    </a>
                </div>
            </div>
        </div>

        <div class="row mt-4">
            <div class="col-md-4">
                <div class="card h-100">
                    <div class="card-body text-center">
                        <i class="fas fa-search fa-3x text-primary mb-3"></i>
                        <h5>Search Movies</h5>
                        <p class="text-muted">Find any movie by title and get comprehensive details</p>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card h-100">
                    <div class="card-body text-center">
                        <i class="fas fa-info-circle fa-3x text-success mb-3"></i>
                        <h5>Detailed Info</h5>
                        <p class="text-muted">Get plot, cast, ratings, and much more information</p>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card h-100">
                    <div class="card-body text-center">
                        <i class="fas fa-star fa-3x text-warning mb-3"></i>
                        <h5>IMDb Ratings</h5>
                        <p class="text-muted">See official IMDb ratings and user reviews</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    // Function for traditional search button
    function performTraditionalSearch() {
        const dropdownSearch = document.querySelector('[data-dropdown-search]');
        if (dropdownSearch) {
            const searchInput = dropdownSearch.querySelector('.dropdown-search-input');
            if (searchInput && searchInput.value.trim()) {
                // Use the fallback form to submit
                document.getElementById('fallback-movie-title').value = searchInput.value.trim();
                document.getElementById('fallback-search-form').submit();
            } else {
                // Focus on the input if empty
                if (searchInput) searchInput.focus();
            }
        }
    }

    // Add some interactivity
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize dropdown search with custom placeholder
        const dropdownContainer = document.querySelector('[data-dropdown-search]');
        if (dropdownContainer) {
            new DropdownSearch(dropdownContainer, {
                placeholder: "Enter movie title (e.g., Inception, The Matrix, Titanic...)"
            });

            // Focus on search input when page loads
            setTimeout(() => {
                const searchInput = dropdownContainer.querySelector('.dropdown-search-input');
                if (searchInput) searchInput.focus();
            }, 100);
        }
        
        // Add loading state when form is submitted
        searchForm.addEventListener('submit', function() {
            const submitBtn = document.querySelector('button[type="submit"]');
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Searching...';
            submitBtn.disabled = true;
        });
    });
</script>
{% endblock %}
